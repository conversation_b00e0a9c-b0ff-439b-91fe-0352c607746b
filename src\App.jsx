import { useEffect, useState } from 'react'

function App() {
  const [members, setMembers] = useState([]);

  useEffect(() => {
    const fetchMembers = async () => {
       const memberData = await fetch('https://geektrust.s3-ap-southeast-1.amazonaws.com/adminui-problem/members.json');
    const mem = await memberData.json();
    setMembers(mem);
    }
    fetchMembers();
  }, [])
  console.log('members list', members)

  return (
    <>
      Pagination 
      <div>
        <h1>
          Employee Data Table
        </h1>
    </div>
      {members?.forEach((member) => {
        <div>{ member.name}</div>
      })}
    </>
  )
}

export default App
